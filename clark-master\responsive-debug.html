<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Responsive Debug - Home Section</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <link
      href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css" />
    <link rel="stylesheet" href="css/animate.css" />
    <link rel="stylesheet" href="css/owl.carousel.min.css" />
    <link rel="stylesheet" href="css/owl.theme.default.min.css" />
    <link rel="stylesheet" href="css/magnific-popup.css" />
    <link rel="stylesheet" href="css/aos.css" />
    <link rel="stylesheet" href="css/ionicons.min.css" />
    <link rel="stylesheet" href="css/flaticon.css" />
    <link rel="stylesheet" href="css/icomoon.css" />
    <link rel="stylesheet" href="css/style.css" />

    <style>
      /* Debug styles to visualize layout issues */
      .debug-border {
        border: 2px solid red !important;
      }
      .debug-text {
        border: 2px solid blue !important;
      }
      .debug-image {
        border: 2px solid green !important;
      }
      .debug-info {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 9999;
        font-size: 12px;
        max-width: 200px;
      }
      .test-controls {
        position: fixed;
        bottom: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 9999;
        font-size: 12px;
      }
      .test-controls button {
        margin: 2px;
        padding: 5px 10px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
      }
      .test-controls button:hover {
        background: #0056b3;
      }
      /* Responsive test frames */
      .responsive-frame {
        transition: all 0.3s ease;
      }
    </style>
  </head>
  <body>
    <div class="debug-info">
      Screen: <span id="screen-size"></span><br />
      Viewport: <span id="viewport-size"></span><br />
      Breakpoint: <span id="breakpoint"></span>
    </div>

    <div class="test-controls">
      <div>Test Responsive Sizes:</div>
      <button onclick="setViewport(320, 568)">iPhone SE</button>
      <button onclick="setViewport(375, 667)">iPhone 8</button>
      <button onclick="setViewport(768, 1024)">iPad</button>
      <button onclick="setViewport(1024, 768)">iPad Landscape</button>
      <button onclick="setViewport(1200, 800)">Desktop</button>
      <button onclick="setViewport(1920, 1080)">Large Desktop</button>
      <button onclick="resetViewport()">Reset</button>
    </div>

    <section id="home-section" class="hero">
      <div class="home-slider owl-carousel">
        <div class="slider-item debug-border">
          <div class="overlay"></div>
          <div class="container">
            <div
              class="row d-md-flex no-gutters slider-text align-items-center justify-content-center debug-border"
              data-scrollax-parent="true"
            >
              <div
                class="one-forth d-flex align-items-center ftco-animate debug-text"
                data-scrollax=" properties: { translateY: '70%' }"
              >
                <div class="text">
                  <span class="subheading">Hello!</span>
                  <h1 class="mb-4 mt-3">I'm <span>Kibru Michael</span></h1>
                  <h2 class="mb-4">A Freelance Full Stack Developer</h2>
                  <p>
                    <a href="#" class="btn btn-primary py-3 px-4">Hire me</a>
                    <a
                      href="#"
                      class="btn btn-white btn-outline-white py-3 px-4"
                      >My works</a
                    >
                  </p>
                </div>
              </div>
              <div
                class="one-third js-fullheight order-md-last img d-flex align-items-center justify-content-center profile-image-container debug-image"
              >
                <div class="profile-image-wrapper">
                  <img
                    src="images/wef.gif"
                    alt="Profile"
                    class="profile-image"
                  />
                  <div class="profile-overlay"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>
    <script src="js/main.js"></script>

    <script>
      function updateDebugInfo() {
        const width = window.innerWidth;
        document.getElementById("screen-size").textContent =
          screen.width + "x" + screen.height;
        document.getElementById("viewport-size").textContent =
          width + "x" + window.innerHeight;

        // Determine breakpoint
        let breakpoint = "";
        if (width < 576) breakpoint = "XS (< 576px)";
        else if (width < 768) breakpoint = "SM (576px - 767px)";
        else if (width < 992) breakpoint = "MD (768px - 991px)";
        else if (width < 1200) breakpoint = "LG (992px - 1199px)";
        else breakpoint = "XL (≥ 1200px)";

        document.getElementById("breakpoint").textContent = breakpoint;
      }

      function setViewport(width, height) {
        // This is for demonstration - actual viewport resizing would need browser dev tools
        document.body.style.width = width + "px";
        document.body.style.height = height + "px";
        document.body.style.overflow = "auto";
        document.body.style.border = "2px solid orange";
        updateDebugInfo();
      }

      function resetViewport() {
        document.body.style.width = "";
        document.body.style.height = "";
        document.body.style.overflow = "";
        document.body.style.border = "";
        updateDebugInfo();
      }

      updateDebugInfo();
      window.addEventListener("resize", updateDebugInfo);

      // Initialize owl carousel
      $(".home-slider").owlCarousel({
        loop: true,
        autoplay: true,
        margin: 0,
        animateOut: "fadeOut",
        animateIn: "fadeIn",
        nav: false,
        autoplayHoverPause: false,
        items: 1,
        navText: [
          "<span class='ion-md-arrow-back'></span>",
          "<span class='ion-chevron-right'></span>",
        ],
        responsive: {
          0: { items: 1 },
          600: { items: 1 },
          1000: { items: 1 },
        },
      });
    </script>
  </body>
</html>
